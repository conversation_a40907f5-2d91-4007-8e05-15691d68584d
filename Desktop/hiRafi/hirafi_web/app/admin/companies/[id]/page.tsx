"use client"

import { useEffect, useState, useRef } from "react"
import { use<PERSON><PERSON><PERSON>, useSearchParams } from "next/navigation"
import { use } from "react"
import { AdminLayout } from "@/components/admin/admin-layout"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { getCompanyById, deleteCompany } from "@/lib/api/admin-api"
import { toast } from "@/components/ui/use-toast"
import { ArrowLeft, Building2, Edit, Trash2, Users, Plus, Mail, Phone, Globe, MapPin, Info } from "lucide-react"
import Link from "next/link"
import { AddUserToCompanyDialog } from "@/components/admin/add-user-to-company-dialog"
import { CompanyUsersList } from "@/components/admin/company-users-list"
import { CompanyTeamsList } from "@/components/admin/company-teams-list"
import { AddTeamDialog } from "@/components/admin/add-team-dialog"
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog"

interface Company {
  id: string
  name: string
  description?: string
  status: 'active' | 'inactive' | 'suspended'
  contactEmail?: string
  contactPhone?: string
  address?: string
  website?: string
  industry?: string
  size?: string
  createdAt: string
  createdBy: string
  settings?: {
    accountType?: 'trial' | 'basic' | 'standard' | 'premium' | 'enterprise'
    runLimit?: number
    runMinuteLimit?: number
    concurrentRunLimit?: number
    scenarioLimit?: number
    generationLimit?: number
    maxUsers?: number
    maxTeams?: number
    maxUsersPerTeam?: number
    maxProjects?: number
    allowExternalUsers?: boolean
    enforceTeamHierarchy?: boolean
    customDomain?: string
    logoUrl?: string
    plugins?: {
      jira?: { enabled: boolean, config?: any }
      xray?: { enabled: boolean, config?: any }
      testrail?: { enabled: boolean, config?: any }
    }
  }
}

export default function CompanyDetailPage({ params }: { params: { id: string } }) {
  // Unwrap params using React.use
  const unwrappedParams = use(params);
  const router = useRouter()
  const [company, setCompany] = useState<Company | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isDeleting, setIsDeleting] = useState(false)
  const [isAddUserDialogOpen, setIsAddUserDialogOpen] = useState(false)
  const [isAddTeamDialogOpen, setIsAddTeamDialogOpen] = useState(false)
  const usersListRef = useRef<{ refresh: () => void }>(null)
  const teamsListRef = useRef<{ refresh: () => void }>(null)
  const searchParams = useSearchParams()
  const defaultTab = searchParams.get('tab') || 'overview'

  useEffect(() => {
    const fetchCompany = async () => {
      setIsLoading(true)
      try {
        const response = await getCompanyById(unwrappedParams.id)

        if (response.success && response.company) {
          setCompany(response.company)
        } else {
          toast({
            title: "Hata",
            description: response.error || "Şirket bilgileri alınamadı.",
            variant: "destructive",
          })
          router.push('/admin/companies')
        }
      } catch (error) {
        toast({
          title: "Hata",
          description: "Şirket bilgileri alınamadı.",
          variant: "destructive",
        })
        router.push('/admin/companies')
      } finally {
        setIsLoading(false)
      }
    }

    fetchCompany()
  }, [unwrappedParams.id, router])

  const handleDeleteCompany = async () => {
    setIsDeleting(true)
    try {
      const response = await deleteCompany(unwrappedParams.id)

      if (response.success) {
        toast({
          title: "Şirket silindi",
          description: "Şirket başarıyla silindi.",
        })
        router.push('/admin/companies')
      } else {
        toast({
          title: "Hata",
          description: response.error || "Şirket silinirken bir hata oluştu.",
          variant: "destructive",
        })
      }
    } catch (error) {
      toast({
        title: "Hata",
        description: "Şirket silinirken bir hata oluştu.",
        variant: "destructive",
      })
    } finally {
      setIsDeleting(false)
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('tr-TR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'active':
        return 'success'
      case 'inactive':
        return 'secondary'
      case 'suspended':
        return 'destructive'
      default:
        return 'default'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'active':
        return 'Aktif'
      case 'inactive':
        return 'Pasif'
      case 'suspended':
        return 'Askıya Alınmış'
      default:
        return status
    }
  }

  if (isLoading) {
    return (
      <AdminLayout>
        <div className="flex h-[50vh] items-center justify-center">
          <div className="flex flex-col items-center gap-2">
            <div className="h-8 w-8 animate-spin rounded-full border-t-2 border-b-2 border-primary"></div>
            <p className="text-sm text-muted-foreground">Şirket bilgileri yükleniyor...</p>
          </div>
        </div>
      </AdminLayout>
    )
  }

  if (!company) {
    return (
      <AdminLayout>
        <div className="flex h-[50vh] flex-col items-center justify-center gap-4">
          <Building2 className="h-16 w-16 text-muted-foreground" />
          <h1 className="text-2xl font-bold">Şirket bulunamadı</h1>
          <p className="text-muted-foreground">İstediğiniz şirket bulunamadı veya erişim izniniz yok.</p>
          <Button asChild>
            <Link href="/admin/companies">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Şirketlere Dön
            </Link>
          </Button>
        </div>
      </AdminLayout>
    )
  }

  return (
    <AdminLayout>
      <div className="flex flex-col gap-6">
          <div className="flex flex-col gap-2 sm:flex-row sm:items-center sm:justify-between">
            <div className="flex items-center gap-2">
              <Button variant="outline" size="icon" asChild>
                <Link href="/admin/companies">
                  <ArrowLeft className="h-4 w-4" />
                </Link>
              </Button>
              <div>
                <h1 className="text-3xl font-bold tracking-tight">{company.name}</h1>
                <div className="flex items-center gap-2">
                  <Badge variant={getStatusBadgeVariant(company.status) as any}>
                    {getStatusText(company.status)}
                  </Badge>
                  {company.industry && (
                    <span className="text-sm text-muted-foreground">{company.industry}</span>
                  )}
                </div>
              </div>
            </div>
            <div className="mt-4 flex gap-2 sm:mt-0">
              <Button variant="outline" asChild>
                <Link href={`/admin/companies/${unwrappedParams.id}/edit`}>
                  <Edit className="mr-2 h-4 w-4" />
                  Düzenle
                </Link>
              </Button>
              <AlertDialog>
                <AlertDialogTrigger asChild>
                  <Button variant="destructive">
                    <Trash2 className="mr-2 h-4 w-4" />
                    Sil
                  </Button>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>Şirketi silmek istediğinize emin misiniz?</AlertDialogTitle>
                    <AlertDialogDescription>
                      Bu işlem geri alınamaz. Bu şirket ve ilişkili tüm veriler kalıcı olarak silinecektir.
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>İptal</AlertDialogCancel>
                    <AlertDialogAction
                      onClick={handleDeleteCompany}
                      className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                      disabled={isDeleting}
                    >
                      {isDeleting ? (
                        <div className="flex items-center">
                          <div className="mr-2 h-4 w-4 animate-spin rounded-full border-t-2 border-b-2 border-current"></div>
                          Siliniyor...
                        </div>
                      ) : (
                        "Şirketi Sil"
                      )}
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            </div>
          </div>

          <Tabs defaultValue={defaultTab}>
            <TabsList className="mb-4">
              <TabsTrigger value="overview">Genel Bakış</TabsTrigger>
              <TabsTrigger value="users">Kullanıcılar</TabsTrigger>
              <TabsTrigger value="teams">Takımlar</TabsTrigger>
              <TabsTrigger value="settings">Ayarlar</TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="space-y-6">
              <div className="grid gap-6 md:grid-cols-2">
                <Card>
                  <CardHeader className="pb-3">
                    <div className="flex items-center gap-2">
                      <Info className="h-4 w-4 text-muted-foreground" />
                      <CardTitle className="text-lg">Şirket Bilgileri</CardTitle>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {company.description && (
                      <div>
                        <h3 className="text-sm font-medium text-muted-foreground">Açıklama</h3>
                        <p className="mt-1">{company.description}</p>
                      </div>
                    )}

                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <h3 className="text-sm font-medium text-muted-foreground">Oluşturulma Tarihi</h3>
                        <p className="mt-1">{formatDate(company.createdAt)}</p>
                      </div>

                      {company.size && (
                        <div>
                          <h3 className="text-sm font-medium text-muted-foreground">Şirket Büyüklüğü</h3>
                          <p className="mt-1">{company.size}</p>
                        </div>
                      )}
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      {company.contactEmail && (
                        <div>
                          <h3 className="text-sm font-medium text-muted-foreground">Şirket Yetkilisi E-posta</h3>
                          <p className="mt-1">{company.contactEmail}</p>
                        </div>
                      )}

                      {company.contactPhone && (
                        <div>
                          <h3 className="text-sm font-medium text-muted-foreground">Şirket Yetkilisi Telefon</h3>
                          <p className="mt-1">{company.contactPhone}</p>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-3">
                    <div className="flex items-center gap-2">
                      <Mail className="h-4 w-4 text-muted-foreground" />
                      <CardTitle className="text-lg">İletişim Bilgileri</CardTitle>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {company.contactEmail && (
                      <div className="flex items-start gap-2">
                        <Mail className="mt-0.5 h-4 w-4 text-muted-foreground" />
                        <div>
                          <h3 className="text-sm font-medium">E-posta</h3>
                          <a href={`mailto:${company.contactEmail}`} className="mt-1 text-blue-600 hover:underline dark:text-blue-400">
                            {company.contactEmail}
                          </a>
                        </div>
                      </div>
                    )}

                    {company.contactPhone && (
                      <div className="flex items-start gap-2">
                        <Phone className="mt-0.5 h-4 w-4 text-muted-foreground" />
                        <div>
                          <h3 className="text-sm font-medium">Telefon</h3>
                          <a href={`tel:${company.contactPhone}`} className="mt-1 text-blue-600 hover:underline dark:text-blue-400">
                            {company.contactPhone}
                          </a>
                        </div>
                      </div>
                    )}

                    {company.website && (
                      <div className="flex items-start gap-2">
                        <Globe className="mt-0.5 h-4 w-4 text-muted-foreground" />
                        <div>
                          <h3 className="text-sm font-medium">Web Sitesi</h3>
                          <a href={company.website} target="_blank" rel="noopener noreferrer" className="mt-1 text-blue-600 hover:underline dark:text-blue-400">
                            {company.website.replace(/^https?:\/\//, '')}
                          </a>
                        </div>
                      </div>
                    )}

                    {company.address && (
                      <div className="flex items-start gap-2">
                        <MapPin className="mt-0.5 h-4 w-4 text-muted-foreground" />
                        <div>
                          <h3 className="text-sm font-medium">Adres</h3>
                          <p className="mt-1">{company.address}</p>
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="users">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between">
                  <div>
                    <CardTitle>Şirket Kullanıcıları</CardTitle>
                    <CardDescription>
                      Bu şirkete ait kullanıcıları yönetin
                    </CardDescription>
                  </div>
                  <Button onClick={() => setIsAddUserDialogOpen(true)}>
                    <Plus className="mr-2 h-4 w-4" />
                    Kullanıcı Ekle
                  </Button>
                </CardHeader>
                <CardContent>
                  <CompanyUsersList ref={usersListRef} companyId={unwrappedParams.id} />
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="teams">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between">
                  <div>
                    <CardTitle>Şirket Takımları</CardTitle>
                    <CardDescription>
                      Bu şirkete ait takımları yönetin
                    </CardDescription>
                  </div>
                  <Button onClick={() => setIsAddTeamDialogOpen(true)}>
                    <Plus className="mr-2 h-4 w-4" />
                    Takım Ekle
                  </Button>
                </CardHeader>
                <CardContent>
                  <CompanyTeamsList ref={teamsListRef} companyId={unwrappedParams.id} />
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="settings">
              <Card>
                <CardHeader>
                  <CardTitle>Şirket Ayarları</CardTitle>
                  <CardDescription>
                    Şirket için özel ayarları görüntüleyin
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* Hesap Türü */}
                  <div className="mb-6 border-b pb-4">
                    <h3 className="text-lg font-medium mb-4">Hesap Bilgileri</h3>
                    <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                      <div>
                        <h3 className="text-sm font-medium text-muted-foreground">Hesap Türü</h3>
                        <p className="mt-1 text-lg font-medium">
                          {company.settings?.accountType ? (
                            {
                              'trial': 'Deneme',
                              'basic': 'Temel',
                              'standard': 'Standart',
                              'premium': 'Premium',
                              'enterprise': 'Kurumsal'
                            }[company.settings.accountType] || company.settings.accountType
                          ) : "Belirtilmemiş"}
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* Koşum ve Senaryo Limitleri */}
                  <div className="mb-6 border-b pb-4">
                    <h3 className="text-lg font-medium mb-4">Koşum ve Senaryo Limitleri</h3>
                    <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
                      <div>
                        <h3 className="text-sm font-medium text-muted-foreground">Koşum Sayısı Limiti</h3>
                        <p className="mt-1 text-lg font-medium">
                          {company.settings?.runLimit === 0 ? "Sınırsız" :
                           company.settings?.runLimit || "Belirtilmemiş"}
                        </p>
                        <p className="text-sm text-muted-foreground">
                          Kalan: {company.settings?.remaining?.runs || 0}
                        </p>
                      </div>

                      <div>
                        <h3 className="text-sm font-medium text-muted-foreground">Koşum Dakikası Limiti</h3>
                        <p className="mt-1 text-lg font-medium">
                          {company.settings?.runMinuteLimit === 0 ? "Sınırsız" :
                           company.settings?.runMinuteLimit || "Belirtilmemiş"}
                        </p>
                        <p className="text-sm text-muted-foreground">
                          Kalan: {company.settings?.remaining?.runMinutes || 0} dakika
                        </p>
                      </div>

                      <div>
                        <h3 className="text-sm font-medium text-muted-foreground">Eşzamanlı Koşum Limiti</h3>
                        <p className="mt-1 text-lg font-medium">
                          {company.settings?.concurrentRunLimit || "Belirtilmemiş"}
                        </p>
                      </div>
                    </div>

                    <div className="grid grid-cols-1 gap-6 md:grid-cols-2 mt-4">
                      {/* Senaryo Limiti alanı kaldırıldı */}

                      <div>
                        <h3 className="text-sm font-medium text-muted-foreground">Generation Limiti</h3>
                        <p className="mt-1 text-lg font-medium">
                          {company.settings?.generationLimit === 0 ? "Sınırsız" :
                           company.settings?.generationLimit || "Belirtilmemiş"}
                        </p>
                        <p className="text-sm text-muted-foreground">
                          Kalan: {company.settings?.remaining?.generations || 0}
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* Kullanıcı ve Takım Limitleri */}
                  <div className="mb-6 border-b pb-4">
                    <h3 className="text-lg font-medium mb-4">Kullanıcı ve Takım Limitleri</h3>
                    <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
                      <div>
                        <h3 className="text-sm font-medium text-muted-foreground">Maksimum Kullanıcı Sayısı</h3>
                        <p className="mt-1 text-lg font-medium">
                          {company.settings?.maxUsers === 0 ? "Sınırsız" :
                           company.settings?.maxUsers || "Belirtilmemiş"}
                        </p>
                      </div>

                      <div>
                        <h3 className="text-sm font-medium text-muted-foreground">Maksimum Takım Sayısı</h3>
                        <p className="mt-1 text-lg font-medium">
                          {company.settings?.maxTeams === 0 ? "Sınırsız" :
                           company.settings?.maxTeams || "Belirtilmemiş"}
                        </p>
                      </div>

                      <div>
                        <h3 className="text-sm font-medium text-muted-foreground">Takım Başına Maksimum Kullanıcı</h3>
                        <p className="mt-1 text-lg font-medium">
                          {company.settings?.maxUsersPerTeam === 0 ? "Sınırsız" :
                           company.settings?.maxUsersPerTeam || "Belirtilmemiş"}
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* Plugin Ayarları */}
                  <div className="mb-6">
                    <h3 className="text-lg font-medium mb-4">Plugin Ayarları</h3>
                    <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
                      <div className="flex items-center gap-2">
                        <div className={`h-3 w-3 rounded-full ${company.settings?.plugins?.jira?.enabled ? 'bg-green-500' : 'bg-gray-300'}`}></div>
                        <h3 className="text-sm font-medium">Jira Entegrasyonu</h3>
                        <span className="text-xs text-muted-foreground">
                          {company.settings?.plugins?.jira?.enabled ? "Aktif" : "Pasif"}
                        </span>
                      </div>

                      <div className="flex items-center gap-2">
                        <div className={`h-3 w-3 rounded-full ${company.settings?.plugins?.xray?.enabled ? 'bg-green-500' : 'bg-gray-300'}`}></div>
                        <h3 className="text-sm font-medium">Xray Entegrasyonu</h3>
                        <span className="text-xs text-muted-foreground">
                          {company.settings?.plugins?.xray?.enabled ? "Aktif" : "Pasif"}
                        </span>
                      </div>

                      <div className="flex items-center gap-2">
                        <div className={`h-3 w-3 rounded-full ${company.settings?.plugins?.testrail?.enabled ? 'bg-green-500' : 'bg-gray-300'}`}></div>
                        <h3 className="text-sm font-medium">TestRail Entegrasyonu</h3>
                        <span className="text-xs text-muted-foreground">
                          {company.settings?.plugins?.testrail?.enabled ? "Aktif" : "Pasif"}
                        </span>
                      </div>
                    </div>
                  </div>

                  {/* Diğer Ayarlar */}
                  <div>
                    <h3 className="text-lg font-medium mb-4">Diğer Ayarlar</h3>
                    <div className="grid grid-cols-1 gap-6 md:grid-cols-2 mt-4">
                      {company.settings?.customDomain && (
                        <div>
                          <h3 className="text-sm font-medium text-muted-foreground">Özel Alan Adı</h3>
                          <p className="mt-1 text-lg font-medium">{company.settings.customDomain}</p>
                        </div>
                      )}

                      {company.settings?.logoUrl && (
                        <div>
                          <h3 className="text-sm font-medium text-muted-foreground">Logo URL</h3>
                          <div className="mt-2 flex items-center gap-2">
                            <img
                              src={company.settings.logoUrl}
                              alt={`${company.name} logo`}
                              className="h-10 w-10 rounded-md object-contain"
                              onError={(e) => {
                                (e.target as HTMLImageElement).src = "https://via.placeholder.com/100?text=Logo";
                              }}
                            />
                            <a
                              href={company.settings.logoUrl}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="text-blue-600 hover:underline dark:text-blue-400"
                            >
                              Görüntüle
                            </a>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </CardContent>
                <CardFooter className="flex justify-end">
                  <Button asChild>
                    <Link href={`/admin/companies/${unwrappedParams.id}/edit`}>
                      <Edit className="mr-2 h-4 w-4" />
                      Ayarları Düzenle
                    </Link>
                  </Button>
                </CardFooter>
              </Card>
            </TabsContent>
          </Tabs>
        </div>

      <AddUserToCompanyDialog
        open={isAddUserDialogOpen}
        onOpenChange={setIsAddUserDialogOpen}
        companyId={unwrappedParams.id}
        companyName={company.name}
        onSuccess={() => usersListRef.current?.refresh()}
      />

      <AddTeamDialog
        open={isAddTeamDialogOpen}
        onOpenChange={setIsAddTeamDialogOpen}
        companyId={unwrappedParams.id}
        companyName={company.name}
        onSuccess={() => teamsListRef.current?.refresh()}
      />
    </AdminLayout>
  )
}
