"use client"

import { useState } from "react"
import { <PERSON><PERSON>, <PERSON>alogContent, DialogDescription, Dialog<PERSON>ooter, <PERSON>alogHeader, DialogTitle } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { toast } from "@/components/ui/use-toast"
import { createCompanyTeam } from "@/lib/api/admin-api"
import { Eye, EyeOff } from "lucide-react"

interface AddTeamDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  companyId: string
  companyName: string
  onSuccess?: () => void
}

export function AddTeamDialog({ open, onOpenChange, companyId, companyName, onSuccess }: AddTeamDialogProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [teamName, setTeamName] = useState("")
  const [teamDescription, setTeamDescription] = useState("")
  const [teamAdminName, setTeamAdminName] = useState("")
  const [teamAdminEmail, setTeamAdminEmail] = useState("")
  const [teamAdminPassword, setTeamAdminPassword] = useState("")
  const [showPassword, setShowPassword] = useState(false)

  const resetForm = () => {
    setTeamName("")
    setTeamDescription("")
    setTeamAdminName("")
    setTeamAdminEmail("")
    setTeamAdminPassword("")
    setShowPassword(false)
  }

  const generateRandomPassword = () => {
    const length = 12
    const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*"
    let password = ""
    for (let i = 0; i < length; i++) {
      password += charset.charAt(Math.floor(Math.random() * charset.length))
    }
    return password
  }

  const handleGeneratePassword = () => {
    const newPassword = generateRandomPassword()
    setTeamAdminPassword(newPassword)
    setShowPassword(true) // Oluşturulduktan sonra şifreyi göster
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!teamName.trim() || !teamAdminName.trim() || !teamAdminEmail.trim() || !teamAdminPassword.trim()) {
      toast({
        title: "Hata",
        description: "Lütfen tüm zorunlu alanları doldurun.",
        variant: "destructive",
      })
      return
    }

    // Email format kontrolü
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(teamAdminEmail)) {
      toast({
        title: "Hata",
        description: "Lütfen geçerli bir email adresi girin.",
        variant: "destructive",
      })
      return
    }

    setIsLoading(true)
    try {
      const response = await createCompanyTeam(companyId, {
        name: teamName.trim(),
        description: teamDescription.trim(),
        teamAdminName: teamAdminName.trim(),
        teamAdminEmail: teamAdminEmail.trim(),
        teamAdminPassword: teamAdminPassword
      })

      if (response.success) {
        toast({
          title: "Başarılı",
          description: "Takım ve team admin başarıyla oluşturuldu.",
        })
        resetForm()
        onOpenChange(false)
        onSuccess?.()
      } else {
        toast({
          title: "Hata",
          description: response.error || "Takım oluşturulamadı.",
          variant: "destructive",
        })
      }
    } catch (error) {
      toast({
        title: "Hata",
        description: "Takım oluşturulurken bir hata oluştu.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleClose = () => {
    if (!isLoading) {
      resetForm()
      onOpenChange(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Yeni Takım Oluştur</DialogTitle>
          <DialogDescription>
            {companyName} şirketi için yeni bir takım oluşturun ve team admin atayın.
          </DialogDescription>
        </DialogHeader>
        
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="teamName">Takım Adı *</Label>
              <Input
                id="teamName"
                value={teamName}
                onChange={(e) => setTeamName(e.target.value)}
                placeholder="Örn: Frontend Takımı"
                disabled={isLoading}
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="teamDescription">Takım Açıklaması</Label>
              <Textarea
                id="teamDescription"
                value={teamDescription}
                onChange={(e) => setTeamDescription(e.target.value)}
                placeholder="Takım hakkında kısa açıklama..."
                disabled={isLoading}
                rows={3}
              />
            </div>

            <div className="border-t pt-4">
              <h4 className="text-sm font-medium mb-3">Team Admin Bilgileri</h4>
              
              <div className="space-y-3">
                <div className="space-y-2">
                  <Label htmlFor="teamAdminName">Team Admin Adı *</Label>
                  <Input
                    id="teamAdminName"
                    value={teamAdminName}
                    onChange={(e) => setTeamAdminName(e.target.value)}
                    placeholder="Örn: Ahmet Yılmaz"
                    disabled={isLoading}
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="teamAdminEmail">Team Admin Email *</Label>
                  <Input
                    id="teamAdminEmail"
                    type="email"
                    value={teamAdminEmail}
                    onChange={(e) => setTeamAdminEmail(e.target.value)}
                    placeholder="Örn: <EMAIL>"
                    disabled={isLoading}
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="teamAdminPassword">Team Admin Şifre *</Label>
                  <div className="flex gap-2">
                    <div className="relative flex-1">
                      <Input
                        id="teamAdminPassword"
                        type={showPassword ? "text" : "password"}
                        value={teamAdminPassword}
                        onChange={(e) => setTeamAdminPassword(e.target.value)}
                        placeholder="Şifre girin veya oluşturun"
                        disabled={isLoading}
                        required
                      />
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                        onClick={() => setShowPassword(!showPassword)}
                        disabled={isLoading}
                      >
                        {showPassword ? (
                          <EyeOff className="h-4 w-4" />
                        ) : (
                          <Eye className="h-4 w-4" />
                        )}
                      </Button>
                    </div>
                    <Button
                      type="button"
                      variant="outline"
                      onClick={handleGeneratePassword}
                      disabled={isLoading}
                    >
                      Oluştur
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              disabled={isLoading}
            >
              İptal
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? (
                <div className="flex items-center">
                  <div className="mr-2 h-4 w-4 animate-spin rounded-full border-t-2 border-b-2 border-current"></div>
                  Oluşturuluyor...
                </div>
              ) : (
                "Takım Oluştur"
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
