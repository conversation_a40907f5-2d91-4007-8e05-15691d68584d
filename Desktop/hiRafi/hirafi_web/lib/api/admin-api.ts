/**
 * Admin API
 *
 * Admin paneli ile ilgili API isteklerini yönetir.
 */

import { adminGet, adminPost, adminPut, adminDel } from './fetch-wrapper';
import { ADMIN_ENDPOINTS } from './constants';

/**
 * Admin dashboard istatistiklerini getirir
 */
export async function getDashboardStats() {
  try {
    const response = await adminGet(ADMIN_ENDPOINTS.DASHBOARD.STATS);

    // Ensure we always have an error message if the request fails
    if (!response.success && !response.error) {
      response.error = 'Failed to fetch dashboard statistics';
      return response;
    }

    // If successful, extract stats from the data property
    if (response.success && response.data) {
      return {
        success: true,
        stats: response.data.stats || response.stats || response.data
      };
    }

    return response;
  } catch (error) {
    console.error('Error in getDashboardStats:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
}

/**
 * Tüm k<PERSON>ıları getirir
 *
 * @param params Filtreleme parametreleri
 */
export async function getUsers(params?: {
  page?: number;
  limit?: number;
  search?: string;
  role?: string;
  companyId?: string;
  active?: string;
}): Promise<{ success: boolean; users?: any[]; total?: number; totalPages?: number; error?: string }> {
  try {
    const response = await adminGet(ADMIN_ENDPOINTS.USERS.GET_ALL, { params });

    // Ensure we always have an error message if the request fails
    if (!response.success && !response.error) {
      response.error = 'Failed to fetch users';
      return response;
    }

    // If successful, extract users from the data property
    if (response.success && response.data) {
      return {
        success: true,
        users: response.data.users || response.data,
        total: response.data.total,
        totalPages: response.data.totalPages
      };
    }

    return response;
  } catch (error) {
    console.error('Error in getUsers:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
}

/**
 * Belirli bir kullanıcıyı getirir
 *
 * @param userId Kullanıcı ID'si
 */
export async function getUserById(userId: string): Promise<{ success: boolean; user?: any; error?: string }> {
  try {
    const response = await adminGet(ADMIN_ENDPOINTS.USERS.GET_BY_ID(userId));

    // Ensure we always have an error message if the request fails
    if (!response.success && !response.error) {
      response.error = 'Failed to fetch user';
      return response;
    }

    // If successful, extract user from the data property
    if (response.success && response.data) {
      return {
        success: true,
        user: response.data.user || response.data
      };
    }

    return response;
  } catch (error) {
    console.error('Error in getUserById:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
}

/**
 * Yeni bir kullanıcı oluşturur
 *
 * @param userData Kullanıcı verileri
 */
export async function createUser(userData: {
  email: string;
  password: string;
  name?: string;
  accountType?: string;
  teamRole?: string;
  companyId?: string;
  teamId?: string;
  active?: boolean;
}) {
  return adminPost(ADMIN_ENDPOINTS.USERS.CREATE, userData, {
    showSuccessToast: true,
    successMessage: 'Kullanıcı başarıyla oluşturuldu'
  });
}

/**
 * Bir kullanıcıyı günceller
 *
 * @param userId Kullanıcı ID'si
 * @param userData Güncellenecek kullanıcı verileri
 */
export async function updateUser(userId: string, userData: {
  email?: string;
  name?: string;
  accountType?: string;
  teamRole?: string;
  companyId?: string | null;
  teamId?: string | null;
  active?: boolean;
}): Promise<{ success: boolean; user?: any; error?: string }> {
  try {
    const response = await adminPut(ADMIN_ENDPOINTS.USERS.UPDATE(userId), userData, {
      showSuccessToast: true,
      successMessage: 'Kullanıcı başarıyla güncellendi'
    });

    // Ensure we always have an error message if the request fails
    if (!response.success && !response.error) {
      response.error = 'Failed to update user';
      return response;
    }

    // If successful, extract user from the data property
    if (response.success && response.data) {
      return {
        success: true,
        user: response.data.user || response.data
      };
    }

    return response;
  } catch (error) {
    console.error('Error in updateUser:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
}

/**
 * Bir kullanıcıyı siler
 *
 * @param userId Kullanıcı ID'si
 */
export async function deleteUser(userId: string) {
  return adminDel(ADMIN_ENDPOINTS.USERS.DELETE(userId), {
    showSuccessToast: true,
    successMessage: 'Kullanıcı başarıyla silindi'
  });
}

/**
 * Tüm şirketleri getirir
 *
 * @param params Filtreleme parametreleri
 */
export async function getCompanies(params?: {
  page?: number;
  limit?: number;
  search?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}) {
  try {
    // Convert page to skip for backend compatibility
    const apiParams = { ...params };
    if (apiParams.page !== undefined && apiParams.limit !== undefined) {
      apiParams.skip = (apiParams.page - 1) * apiParams.limit;
      // Keep page parameter for frontend pagination
    }

    // Add search parameter to query if provided
    if (apiParams.search && apiParams.search.trim() !== '') {
      apiParams.name = apiParams.search; // Use name field for searching
    }

    console.log('Sending company request with params:', apiParams);
    const response = await adminGet(ADMIN_ENDPOINTS.COMPANIES.GET_ALL, { params: apiParams });

    // Ensure we always have an error message if the request fails
    if (!response.success && !response.error) {
      response.error = 'Failed to fetch companies';
      return response;
    }

    // If successful, extract companies from the data property
    if (response.success && response.data) {
      // Calculate total pages based on total and limit
      const total = response.data.total || (response.data.companies ? response.data.companies.length : 0);
      const limit = params?.limit || 10;
      const totalPages = Math.ceil(total / limit);

      return {
        success: true,
        companies: response.data.companies || response.data,
        total: total,
        totalPages: totalPages
      };
    }

    return response;
  } catch (error) {
    console.error('Error in getCompanies:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
}

/**
 * Belirli bir şirketi getirir
 *
 * @param companyId Şirket ID'si
 */
export async function getCompanyById(companyId: string): Promise<{ success: boolean; company?: any; error?: string }> {
  try {
    const response = await adminGet(ADMIN_ENDPOINTS.COMPANIES.GET_BY_ID(companyId));

    // Ensure we always have an error message if the request fails
    if (!response.success && !response.error) {
      response.error = 'Failed to fetch company';
      return response;
    }

    // If successful, extract company from the data property
    if (response.success && response.data) {
      return {
        success: true,
        company: response.data.company || response.company || response.data
      };
    }

    return response;
  } catch (error) {
    console.error('Error in getCompanyById:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
}

/**
 * Yeni bir şirket oluşturur
 *
 * @param companyData Şirket verileri
 */
export async function createCompany(companyData: {
  name: string;
  description?: string;
  status?: string;
  contactEmail?: string;
  contactPhone?: string;
  address?: string;
  website?: string;
  industry?: string;
  size?: string;
  settings?: any;
}) {
  return adminPost(ADMIN_ENDPOINTS.COMPANIES.CREATE, companyData, {
    showSuccessToast: true,
    successMessage: 'Şirket başarıyla oluşturuldu'
  });
}

/**
 * Bir şirketi günceller
 *
 * @param companyId Şirket ID'si
 * @param companyData Güncellenecek şirket verileri
 */
export async function updateCompany(companyId: string, companyData: {
  name?: string;
  description?: string;
  status?: string;
  contactEmail?: string;
  contactPhone?: string;
  address?: string;
  website?: string;
  industry?: string;
  size?: string;
  settings?: any;
}) {
  return adminPut(ADMIN_ENDPOINTS.COMPANIES.UPDATE(companyId), companyData, {
    showSuccessToast: true,
    successMessage: 'Şirket başarıyla güncellendi'
  });
}

/**
 * Bir şirketi siler
 *
 * @param companyId Şirket ID'si
 */
export async function deleteCompany(companyId: string) {
  return adminDel(ADMIN_ENDPOINTS.COMPANIES.DELETE(companyId), {
    showSuccessToast: true,
    successMessage: 'Şirket başarıyla silindi'
  });
}

/**
 * Bir şirketin kalan kullanım bilgilerini günceller
 *
 * @param companyId Şirket ID'si
 * @param remaining Güncellenecek kalan kullanım bilgileri
 */
export async function updateCompanyRemaining(companyId: string, remaining: {
  runs?: number;
  runMinutes?: number;
  generations?: number;
}) {
  return adminPut(ADMIN_ENDPOINTS.COMPANIES.REMAINING(companyId), remaining, {
    showSuccessToast: true,
    successMessage: 'Şirket kalan kullanım bilgileri başarıyla güncellendi'
  });
}

/**
 * Bir şirketin kullanıcılarını getirir
 *
 * @param companyId Şirket ID'si
 */
export async function getCompanyUsers(companyId: string): Promise<{ success: boolean; users?: any[]; error?: string }> {
  try {
    const response = await adminGet(ADMIN_ENDPOINTS.COMPANIES.USERS(companyId));

    // Ensure we always have an error message if the request fails
    if (!response.success && !response.error) {
      response.error = 'Failed to fetch company users';
      return response;
    }

    // If successful, extract users from the data property
    if (response.success && response.data) {
      return {
        success: true,
        users: response.data.users || response.users || response.data
      };
    }

    return response;
  } catch (error) {
    console.error('Error in getCompanyUsers:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
}





/**
 * Şirketin takımlarını getirir
 *
 * @param companyId Şirket ID'si
 */
export async function getCompanyTeams(companyId: string): Promise<{ success: boolean; teams?: any[]; error?: string }> {
  try {
    const response = await adminGet(ADMIN_ENDPOINTS.COMPANIES.TEAMS(companyId));
    return response;
  } catch (error) {
    console.error('Error fetching company teams:', error);
    return {
      success: false,
      error: 'Failed to fetch company teams'
    };
  }
}

/**
 * Şirkete yeni takım oluşturur (team admin ile birlikte)
 *
 * @param companyId Şirket ID'si
 * @param teamData Takım ve team admin bilgileri
 */
export async function createCompanyTeam(companyId: string, teamData: {
  name: string;
  description?: string;
  teamAdminEmail: string;
  teamAdminPassword: string;
  teamAdminName?: string;
}): Promise<{ success: boolean; teamId?: string; teamAdminUserId?: string; error?: string }> {
  try {
    const response = await adminPost(ADMIN_ENDPOINTS.COMPANIES.TEAMS(companyId), teamData, {
      showSuccessToast: true,
      successMessage: 'Takım ve team admin başarıyla oluşturuldu'
    });
    return response;
  } catch (error) {
    console.error('Error creating company team:', error);
    return {
      success: false,
      error: 'Failed to create company team'
    };
  }
}

/**
 * Takımı siler
 *
 * @param teamId Takım ID'si
 */
export async function deleteTeam(teamId: string): Promise<{ success: boolean; error?: string }> {
  try {
    const response = await adminDelete(`/teams/${teamId}`, {
      showSuccessToast: true,
      successMessage: 'Takım başarıyla silindi'
    });
    return response;
  } catch (error) {
    console.error('Error deleting team:', error);
    return {
      success: false,
      error: 'Failed to delete team'
    };
  }
}

/**
 * Takım üyelerini getirir
 *
 * @param teamId Takım ID'si
 */
export async function getTeamMembers(teamId: string): Promise<{ success: boolean; members?: any[]; error?: string }> {
  try {
    const response = await adminGet(`/teams/${teamId}/members`);
    return response;
  } catch (error) {
    console.error('Error fetching team members:', error);
    return {
      success: false,
      error: 'Failed to fetch team members'
    };
  }
}

/**
 * Takım üyesini siler
 *
 * @param teamId Takım ID'si
 * @param memberId Üye ID'si
 */
export async function removeTeamMember(teamId: string, memberId: string): Promise<{ success: boolean; error?: string }> {
  try {
    const response = await adminDelete(`/teams/${teamId}/members/${memberId}`, {
      showSuccessToast: true,
      successMessage: 'Takım üyesi başarıyla silindi'
    });
    return response;
  } catch (error) {
    console.error('Error removing team member:', error);
    return {
      success: false,
      error: 'Failed to remove team member'
    };
  }
}

/**
 * Bir takıma üye ekler
 *
 * @param teamId Takım ID'si
 * @param userId Kullanıcı ID'si
 * @param roleId Rol ID'si
 */
export async function addTeamMember(teamId: string, userId: string, roleId: string = "team_member"): Promise<{ success: boolean; member?: any; error?: string }> {
  try {
    const response = await adminPost(`/teams/${teamId}/members`, { userId, roleId }, {
      showSuccessToast: true,
      successMessage: 'Takım üyesi başarıyla eklendi'
    });

    // Ensure we always have an error message if the request fails
    if (!response.success && !response.error) {
      response.error = 'Failed to add team member';
      return response;
    }

    // If successful, extract member from the data property
    if (response.success && response.data) {
      return {
        success: true,
        member: response.data.member || response.data
      };
    }

    return response;
  } catch (error) {
    console.error('Error in addTeamMember:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
}



/**
 * Admin ayarlarını getirir
 */
export async function getAdminSettings() {
  try {
    const response = await adminGet(ADMIN_ENDPOINTS.SETTINGS.GET_ALL);

    // Ensure we always have an error message if the request fails
    if (!response.success && !response.error) {
      response.error = 'Failed to fetch admin settings';
      return response;
    }

    // If successful, extract data from the data property
    if (response.success && response.data) {
      return {
        success: true,
        data: response.data
      };
    }

    return response;
  } catch (error) {
    console.error('Error in getAdminSettings:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
}

/**
 * Admin ayarlarını günceller
 *
 * @param settings Güncellenecek ayarlar
 */
export async function updateAdminSettings(settings: any) {
  try {
    const response = await adminPut(ADMIN_ENDPOINTS.SETTINGS.UPDATE, settings, {
      showSuccessToast: true,
      successMessage: 'Admin ayarları başarıyla güncellendi'
    });

    // Ensure we always have an error message if the request fails
    if (!response.success && !response.error) {
      response.error = 'Failed to update admin settings';
      return response;
    }

    return response;
  } catch (error) {
    console.error('Error in updateAdminSettings:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
}

/**
 * Sistem sağlık durumunu getirir
 */
export async function getHealthData() {
  try {
    const response = await adminGet(ADMIN_ENDPOINTS.HEALTH);

    // Ensure we always have an error message if the request fails
    if (!response.success && !response.error) {
      response.error = 'Failed to fetch health data';
      return response;
    }

    // If successful, extract data from the data property
    if (response.success && response.data) {
      return {
        success: true,
        data: response.data
      };
    }

    return response;
  } catch (error) {
    console.error('Error in getHealthData:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
}

/**
 * Kuyruk izleme verilerini getirir
 */
export async function getQueueMonitorData(signal?: AbortSignal) {
  try {
    const response = await adminGet(ADMIN_ENDPOINTS.QUEUE_MONITOR, { signal });

    // Ensure we always have an error message if the request fails
    if (!response.success && !response.error) {
      response.error = 'Failed to fetch queue monitoring data';
      return response;
    }

    // If successful, return the data
    return response;
  } catch (error) {
    console.error('Error in getQueueMonitorData:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
}

/**
 * Sıkışmış bir testi siler
 *
 * @param testId Test ID'si
 */
export async function deleteStuckTest(testId: string) {
  try {
    const response = await adminDel(ADMIN_ENDPOINTS.STUCK_TESTS.DELETE(testId), {
      showSuccessToast: true,
      successMessage: 'Sıkışmış test başarıyla silindi'
    });

    // Ensure we always have an error message if the request fails
    if (!response.success && !response.error) {
      response.error = 'Failed to delete stuck test';
      return response;
    }

    return response;
  } catch (error) {
    console.error('Error in deleteStuckTest:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
}

/**
 * Aktif bir testi zorla tamamlar
 *
 * @param testId Test ID'si
 */
export async function completeTest(testId: string) {
  try {
    const response = await adminPost(ADMIN_ENDPOINTS.COMPLETE_TEST(testId), {}, {
      showSuccessToast: true,
      successMessage: 'Test başarıyla tamamlandı'
    });

    // Ensure we always have an error message if the request fails
    if (!response.success && !response.error) {
      response.error = 'Failed to complete test';
      return response;
    }

    return response;
  } catch (error) {
    console.error('Error in completeTest:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
}

/**
 * Tüm node'ları getirir
 */
export async function getNodes() {
  try {
    const response = await adminGet(ADMIN_ENDPOINTS.NODES);

    // Ensure we always have an error message if the request fails
    if (!response.success && !response.error) {
      response.error = 'Failed to fetch nodes';
      return response;
    }

    // If successful, extract data from the data property
    if (response.success && response.data) {
      return {
        success: true,
        data: response.data
      };
    }

    return response;
  } catch (error) {
    console.error('Error in getNodes:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
}

/**
 * Admin şifresini değiştirir
 *
 * @param currentPassword Mevcut şifre
 * @param newPassword Yeni şifre
 */
export async function changeAdminPassword(currentPassword: string, newPassword: string) {
  try {
    const response = await adminPost('/admin/change-password', { currentPassword, newPassword }, {
      showSuccessToast: true,
      successMessage: 'Şifre başarıyla değiştirildi'
    });

    // Ensure we always have an error message if the request fails
    if (!response.success && !response.error) {
      response.error = 'Failed to change password';
      return response;
    }

    return response;
  } catch (error) {
    console.error('Error in changeAdminPassword:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
}

/**
 * Aktif testleri getirir
 */
export async function getActiveTests() {
  try {
    const response = await adminGet(ADMIN_ENDPOINTS.ACTIVE_TESTS.GET_ALL);

    // Ensure we always have an error message if the request fails
    if (!response.success && !response.error) {
      response.error = 'Failed to fetch active tests';
      return response;
    }

    // If successful, extract data from the response
    if (response.success && response.data) {
      return {
        success: true,
        activeTests: response.data.activeTests || response.data,
        count: response.data.count || 0,
        timestamp: response.data.timestamp
      };
    }

    return response;
  } catch (error) {
    console.error('Error in getActiveTests:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
}

/**
 * Belirli bir aktif testi zorla tamamlar
 *
 * @param testId Test ID'si
 */
export async function completeActiveTest(testId: string) {
  try {
    const response = await adminPost(ADMIN_ENDPOINTS.ACTIVE_TESTS.COMPLETE(testId), {}, {
      showSuccessToast: true,
      successMessage: 'Aktif test başarıyla tamamlandı'
    });

    // Ensure we always have an error message if the request fails
    if (!response.success && !response.error) {
      response.error = 'Failed to complete active test';
      return response;
    }

    return response;
  } catch (error) {
    console.error('Error in completeActiveTest:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
}

/**
 * Belirli bir aktif testi siler
 *
 * @param testId Test ID'si
 */
export async function removeActiveTest(testId: string) {
  try {
    const response = await adminDel(ADMIN_ENDPOINTS.ACTIVE_TESTS.REMOVE(testId), {
      showSuccessToast: true,
      successMessage: 'Aktif test başarıyla silindi'
    });

    // Ensure we always have an error message if the request fails
    if (!response.success && !response.error) {
      response.error = 'Failed to remove active test';
      return response;
    }

    return response;
  } catch (error) {
    console.error('Error in removeActiveTest:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
}

/**
 * Tüm aktif testleri zorla tamamlar
 */
export async function completeAllActiveTests() {
  try {
    const response = await adminPost(ADMIN_ENDPOINTS.ACTIVE_TESTS.COMPLETE_ALL, {}, {
      showSuccessToast: true,
      successMessage: 'Tüm aktif testler başarıyla tamamlandı'
    });

    // Ensure we always have an error message if the request fails
    if (!response.success && !response.error) {
      response.error = 'Failed to complete all active tests';
      return response;
    }

    // If successful, extract details from the response
    if (response.success && response.data) {
      return {
        success: true,
        message: response.data.message || response.message,
        details: response.data.details || response.details
      };
    }

    return response;
  } catch (error) {
    console.error('Error in completeAllActiveTests:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
}

/**
 * Tüm aktif testleri siler
 */
export async function removeAllActiveTests() {
  try {
    const response = await adminDel(ADMIN_ENDPOINTS.ACTIVE_TESTS.REMOVE_ALL, {
      showSuccessToast: true,
      successMessage: 'Tüm aktif testler başarıyla silindi'
    });

    // Ensure we always have an error message if the request fails
    if (!response.success && !response.error) {
      response.error = 'Failed to remove all active tests';
      return response;
    }

    // If successful, extract details from the response
    if (response.success && response.data) {
      return {
        success: true,
        message: response.data.message || response.message,
        details: response.data.details || response.details
      };
    }

    return response;
  } catch (error) {
    console.error('Error in removeAllActiveTests:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
}

/**
 * Nuclear clear: Kuyruğu tamamen temizler (son çare)
 * Tüm güvenlik kontrollerini bypass eder ve kuyruktaki her şeyi siler
 */
export async function nuclearClearQueue(queueName?: string) {
  return adminPost(ADMIN_ENDPOINTS.QUEUE.NUCLEAR_CLEAR, { 
    queueName,
    confirmationCode: 'NUCLEAR_CLEAR_CONFIRMED' // Ensure confirmation is always sent
  }, {
    showSuccessToast: true,
    successMessage: `Kuyruk ${queueName || 'test-queue'} başarıyla temizlendi`,
    showErrorToast: true,
    errorMessage: `Kuyruk ${queueName || 'test-queue'} temizlenirken hata oluştu`
  });
}

/**
 * Gets all Redis data for admin viewing.
 */
export async function getAllRedisData(signal?: AbortSignal): Promise<{ success: boolean; data?: any; message?: string; error?: string }> {
  try {
    const response = await adminGet(ADMIN_ENDPOINTS.REDIS.GET_DATA, { signal });

    // Ensure we always have an error message if the request fails
    if (!response.success && !response.error) {
      response.error = 'Failed to fetch Redis data';
      return response;
    }

    return response;
  } catch (error) {
    console.error('Error in getAllRedisData:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Bilinmeyen bir hata oluştu.'
    };
  }
}

/**
 * Deletes a specific key from a Redis database.
 */
export async function deleteRedisKey(database: number, key: string): Promise<{ success: boolean; message?: string; error?: string }> {
  try {
    const response = await adminDel(ADMIN_ENDPOINTS.REDIS.DELETE_KEY, { database, key }, {
      showSuccessToast: true,
      successMessage: `Key "${key}" başarıyla silindi.`,
      showErrorToast: true,
      errorMessage: 'Redis key silinirken bir hata oluştu.'
    });
    return response;
  } catch (error) {
    console.error('Error in deleteRedisKey:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Bilinmeyen bir hata oluştu.'
    };
  }
}

/**
 * Purges all data from all Redis databases.
 * THIS IS A HIGHLY DESTRUCTIVE OPERATION.
 */
export async function purgeAllRedisData(): Promise<{ success: boolean; message?: string; error?: string }> {
  try {
    // No body is needed for this request as per the backend implementation
    const response = await adminPost(ADMIN_ENDPOINTS.REDIS.PURGE_ALL, {}, {
      showSuccessToast: true,
      successMessage: 'Tüm Redis verileri başarıyla temizlendi.',
      showErrorToast: true,
      errorMessage: 'Redis verileri temizlenirken bir hata oluştu.'
    });
    return response; // fetch-wrapper already structures this with success/error/message
  } catch (error) {
    console.error('Error in purgeAllRedisData:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Bilinmeyen bir hata oluştu.'
    };
  }
}
